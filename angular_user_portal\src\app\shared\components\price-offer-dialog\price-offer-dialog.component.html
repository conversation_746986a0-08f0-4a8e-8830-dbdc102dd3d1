<div class="p-6 text-sm bg-white rounded-lg shadow-lg flex flex-col h-full">
  <h2 class="font-semibold text-indigo-600 flex-grow-0 flex justify-between">
    <div>{{ 'UserPortal:PriceOffer' | i18n }}</div>
    <div>
      <mat-icon (click)="details$.set(!details$())">arrow_drop_down_circle</mat-icon>
    </div>
  </h2>

  <div class="flex-grow">
    @if (details$()) {
    <hr class="my-3" />
    <div class="mb-3">
      <div class="grid grid-cols-3 gap-2">
        @for (item of filters(); track $index) {
        <button
          class="p-2 rounded-md border bg-white text-black [&.sel]:text-white [&.sel]:bg-gray-200"
          [ngClass]="{ sel: filter$() == item }"
          (click)="filter$.set(item)"
        >
          {{ item | i18n }} ({{ pricingTypeOptionsCounts()[item] || 0 }})
        </button>
        }
      </div>
    </div>
    <div class="space-y-2">
      <div class="grid grid-cols-1 gap-3">
        @for (item of filteredBillLineItems$(); track $index) {
        <div class="grid grid-cols-2 gap-y-2 gap-x-4 p-3 rounded-md bg-main_light_gray text-start">
          <div class="text-main_gray">{{ 'UserPortal:pricingItemDisplayName' | i18n }}</div>
          <div>{{ item.pricingItemDisplayName }}</div>
          <div class="text-main_gray">{{ 'UserPortal:unitPrice' | i18n }}</div>
          <div>{{ item.unitPrice }}</div>
          <div class="text-main_gray">{{ 'UserPortal:quantity' | i18n }}</div>
          <div>{{ item.quantity }}</div>
          <div class="text-main_gray">{{ 'UserPortal:pricingType' | i18n }}</div>
          <div>{{ item.pricingType }} ({{ item.requestedMonths }} {{ 'month' | i18n }})</div>
          <div class="text-main_gray">{{ 'UserPortal:price' | i18n }}</div>
          <div>{{ item.price }}</div>
        </div>
        }
      </div>
    </div>

    <hr class="my-3" />

    <div>
      <div class="text-center">
        <div class="text-main_perple">
          {{ 'UserPortal:discounts' | i18n }} ({{ bills().appliedDiscounts.length }})
        </div>
      </div>
      <div class="grid grid-cols-1 gap-3">
        @for (item of bills().appliedDiscounts; track $index) {
        <div class="relative">
          <img src="/assets/images/svg/offer.svg" alt="offer" class="absolute -left-5" />
          <div class="p-3 rounded-md border border-red-500 bg-main_light_gray text-start">
            <!-- <div class="flex my-3">
                <div class="mx-12 text-main_gray">{{ 'UserPortal:discountType' | i18n }}</div>
                <div>{{ item. }}</div>
              </div> -->
            <div class="flex my-3">
              <div class="mx-12 text-main_gray">{{ 'UserPortal:appliedAt' | i18n }}</div>
              <div>{{ item.appliedAt | date : 'medium' : '' : '' }}</div>
            </div>
            <div class="flex my-3">
              <div class="mx-12 text-main_gray">{{ 'UserPortal:value' | i18n }}</div>
              <div>
                <span class="p-1 px-4 font-semibold text-white rounded-lg bg-main_perple">
                  {{ item.isPercentage ? item.value * 100 : item.value }}
                  {{ item.isPercentage ? '%' : ('SP' | i18n) }}
                </span>
              </div>
            </div>
          </div>
        </div>
        }
      </div>
    </div>
    }

    <div class="m-2">
      <div class="text-main_gray">{{ 'UserPortal:total' | i18n }}</div>
      <div>
        <div
          class="p-1 px-4 mx-4 font-semibold text-center bg-gray-200 rounded-lg text-main_perple"
        >
          {{ bills().total }} {{ 'SP' | i18n }}
        </div>
      </div>
    </div>
  </div>
  <div class="flex justify-center gap-4 mt-4 text-sm flex-grow-0">
    <button mat-button mat-flat-button class="cancleButton" (click)="closeDialog()">
      {{ 'UserPortal:cancel' | i18n }}
    </button>
    <button mat-button mat-flat-button (click)="closeDialog(data.pay)">
      {{ (data.pay ? 'UserPortal:pay' : 'UserPortal:ok') | i18n }}
    </button>
  </div>
</div>
