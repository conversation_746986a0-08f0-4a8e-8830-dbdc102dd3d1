import { CommonModule, NgStyle } from '@angular/common';
import { Component, inject, input, OnInit, signal } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { Router } from '@angular/router';
import { RouteService } from '@proxy/mobile/routes';
import {
  changeNodeDto,
  CustomLine,
  CustomMarker,
  MapComponent,
} from '@shared/components/map/map.component';
import { ValidationComponent } from '@shared/components/validation/validation.component';
import { colors } from '@shared/constants/colors.constants';
import { colorToHex, hexToColor } from '@shared/functions/hex-to-color';
import { live_icon } from '@shared/helper-assets/live';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { latLng, LatLngTuple, Layer, MapOptions } from 'leaflet';

@Component({
  selector: 'app-add-stop-point',
  standalone: true,
  templateUrl: './add-stop-point.component.html',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatButtonModule,
    MatInputModule,
    MapComponent,
    LanguagePipe,
    MatSelectModule,
    NgStyle,
    ValidationComponent,
  ],
})
export class AddStopPointComponent implements OnInit {
  private fb = inject(FormBuilder);

  colors = signal(colors);
  form: FormGroup = this.fb.group({
    name: [''],
    hexColor: ['#ffffff'],
  });
  ngOnInit(): void {}

  save() {}

  close() {}
}
