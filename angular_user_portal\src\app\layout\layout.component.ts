import { ProfileService } from '@abp/ng.account.core/proxy';
import {
  EnvironmentService,
  LocalizationModule,
  RoutesService,
  SessionStateService,
} from '@abp/ng.core';
import { Component, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatIconButton } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIcon } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatToolbar } from '@angular/material/toolbar';
import { Router, RouterLink, RouterOutlet } from '@angular/router';
import { logout } from '@shared/functions/logout';
import { FcmService } from '@shared/services/fcm.service';
import { AlertService, LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { catchError, map, of } from 'rxjs';
import { BreadcrumbComponent } from './breadcrumb/breadcrumb.component';
import { TrackAccountService } from '@proxy/mobile/track-accounts';
import { FEATURES } from '@shared/constants/features-token';

@Component({
  selector: 'app-layout',
  standalone: true,
  imports: [
    MatSidenavModule,
    MatListModule,
    MatExpansionModule,
    MatIcon,
    MatIconButton,
    MatToolbar,
    MatMenuModule,
    RouterOutlet,
    RouterLink,
    LocalizationModule,
    BreadcrumbComponent,
    LanguagePipe,
    MatIcon,
  ],
  templateUrl: './layout.component.html',
  styleUrl: './layout.component.scss',
})
export class LayoutComponent {
  private routes = inject(RoutesService);
  private session = inject(SessionStateService);
  private trackAccountService = inject(TrackAccountService);
  private profile = inject(ProfileService);
  private features = inject(FEATURES);
  private environment = inject(EnvironmentService);
  private router = inject(Router);
  private alert = inject(AlertService);
  fcm = inject(FcmService);

  protected isAuthed = toSignal(
    this.profile.get().pipe(
      catchError(() => of(false)),
      map(Boolean)
    )
  );

  protected appName = this.environment.getEnvironment().application.name;
  protected routes$ = this.routes.visible$;
  protected routess = [
    { icon: 'home', link: '/main', name: 'main' },
    {
      icon: 'notification_important',
      link: '/main/notifications',
      name: 'notifications',
      feature: 'GoTrack.Notifications',
    },
    {
      icon: 'settings',
      link: '/main/services',
      name: 'services',
      show: localStorage.getItem('AssociationType') == 'O',
    },
    {
      icon: 'insert_drive_file',
      link: '/main/report',
      name: 'report',
      features: 'GoTrack.Reports',
    },
    { icon: 'dashboard', link: '/more', name: 'more' },
  ];

  ngOnInit(): void {
    this.fcm.getAndRegisterToken().subscribe();
    this.fcm.messages$.subscribe(val => {
      return val;
    });
    this.trackAccountService.getFeatures(localStorage.getItem('trackAccountId')).subscribe(val => {
      const r = val.reduce((acc, cu) => {
        return { ...acc, [cu.name]: cu.value == 'True' };
      }, {});
      this.features.set(r);
    });
  }

  changeLang(language: 'ar' | 'en') {
    this.session.setLanguage(language);
  }

  logout() {
    logout(this.fcm);
  }

  ngOnDestroy(): void {
    this.fcm.deleteToken();
    localStorage.removeItem('AssociationType');
    localStorage.removeItem('trackAccountId');
  }

  navigate(item: { link: string; show?: boolean }) {
    if (!item.show) {
      this.router.navigate([item.link]);
    } else {
      this.alert.warning('You are an observer not an owner');
    }
  }
}
