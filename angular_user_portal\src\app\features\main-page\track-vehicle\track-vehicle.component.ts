import { LocalizationModule } from '@abp/ng.core';
import { NgStyle } from '@angular/common';
import { Component, inject, input, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { RouterLink } from '@angular/router';
import { MonitoringService } from '@proxy/mobile/monitoring';
import { LiveLocationDto } from '@proxy/mobile/monitoring/dtos/live-locations';
import { VehicleReportService } from '@proxy/mobile/reports';
import { VehicleDistanceAverageAndMaxSpeedReportDto } from '@proxy/mobile/reports/dto';
import { VehicleService } from '@proxy/mobile/vehicles';
import { VehicleDto } from '@proxy/mobile/vehicles/dtos';
import { car_svg } from '@shared';
import { CustomMarker, MapComponent } from '@shared/components/map/map.component';
import { hexToColor } from '@shared/functions/hex-to-color';
import { HexToColorPipe } from '@shared/pipes/hex-to-color.pipe';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { latLng, LatLngTuple, Layer, MapOptions } from 'leaflet';
import { combineLatest, interval, map, startWith, switchMap } from 'rxjs';

@Component({
  selector: 'app-track-vehicle',
  templateUrl: './track-vehicle.component.html',
  standalone: true,
  imports: [
    LocalizationModule,
    NgStyle,
    FormsModule,
    ReactiveFormsModule,
    LanguagePipe,
    MatButtonModule,
    MatIcon,
    RouterLink,
    HexToColorPipe,
    MapComponent,
  ],
  styles: `
  :host {
  --mdc-outlined-text-field-container-shape: 10px;
  --mat-form-field-container-vertical-padding: 10px;
  --mat-form-field-container-height: 44px;
  --mdc-filled-button-container-shape: 10px;
  --mdc-filled-button-container-color: #7164e4;
  }
  `,
})
export class TrackVehicleComponent {
  vehicleService = inject(VehicleService);
  monitoringService = inject(MonitoringService);
  vehicleReportService = inject(VehicleReportService);
  selectedVehcileInfo$: WritableSignal<VehicleDistanceAverageAndMaxSpeedReportDto | any> = signal(
    {}
  );

  node$ = signal<Layer[]>([]);

  options$ = signal<MapOptions>({});

  id = input<string | null>(null);

  interval$ = interval(5000).pipe(startWith(null));

  vehicle$ = signal<VehicleDto | any>({});

  monitorInfo$;

  constructor() {
    this.monitorInfo$ = combineLatest([toObservable(this.id), this.interval$]).pipe(
      switchMap(val => {
        return combineLatest([
          this.report(val[0]).pipe(
            map(report => {
              this.selectedVehcileInfo$.set(report);
            })
          ),
          this.monitoringService
            .postLiveLocation({
              onlySpeed: true,
              vehicleIds: [val[0]],
            })
            .pipe(
              map(v => {
                v.map(val => {
                  const { longitude, latitude, value } = val.warpGtses[0].values[0];
                  this.node$.set([]);
                  this.selectedVehcileInfo$.update(v => {
                    return { ...v, averageSpeed: value };
                  });
                  this.options$.update(v => {
                    return { ...v, center: latLng(+latitude, +longitude), zoom: 17 };
                  });
                  this.addMarker(+latitude, +longitude, val);
                });
              })
            ),
        ]);
      }),
      takeUntilDestroyed()
    );
  }
  async ngOnInit() {
    await this.vehicleService.get(this.id()).subscribe(res => {
      res.colorHex = hexToColor(res.colorHex);
      this.vehicle$.set(res);
    });

    this.monitorInfo$.subscribe();
  }

  async addMarker(latitude: number, longitude: number, info: LiveLocationDto) {
    this.node$.update(val => {
      return [
        ...val,
        CustomMarker({
          icon: car_svg(this.vehicle$().colorHex),
          latlang: [latitude, longitude] as LatLngTuple,
        }),
      ];
    });
  }

  report(vehicleId: string, from: Date = new Date(), to: Date = new Date()) {
    return this.vehicleReportService.getVehicleDistanceAverageAndMaxSpeedReport({
      vehicleId: vehicleId,
      ignoreSpeedUnder: 0,
      fromDate: from.toISOString(),
      toDate: to.toISOString(),
    });
  }
}
