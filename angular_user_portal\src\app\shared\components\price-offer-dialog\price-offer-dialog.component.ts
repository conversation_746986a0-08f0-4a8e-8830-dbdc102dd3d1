import { Date<PERSON><PERSON><PERSON>, Ng<PERSON>lass } from '@angular/common';
import { Component, computed, inject, signal } from '@angular/core';
import { <PERSON><PERSON><PERSON>on, MatMiniFabButton } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { BillService } from '@proxy/mobile/payments/bills';
import { BillDto, BillLineItemDto } from '@proxy/mobile/payments/bills/dtos';
import { BillStatus } from '@proxy/payments/bills';
import { pricingTypeOptions } from '@proxy/payments/pricing-items';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { firstValueFrom } from 'rxjs';

interface BillLineItemDtoWithPrice extends BillLineItemDto {
  price: number;
}
@Component({
  selector: 'app-price-offer-dialog',
  standalone: true,
  templateUrl: `./price-offer-dialog.component.html`,
  imports: [Mat<PERSON><PERSON><PERSON>, LanguagePipe, Ng<PERSON>lass, DatePipe, MatIcon],
})
export class PriceOfferDialogComponent {
  dialogRef = inject(MatDialogRef<PriceOfferDialogComponent>);
  data: BillDto & { id: string; pay: boolean } = inject(MAT_DIALOG_DATA);
  billService = inject(BillService);

  filter$ = signal('all');
  details$ = signal(false);
  filters = signal<string[]>(['all', ...pricingTypeOptions.map(x => x.value)]);

  pricingTypeOptionsCounts = signal<any>({});

  bills = signal<BillDto & { billLineItems: BillLineItemDtoWithPrice[] }>({
    status: BillStatus.Draft,
    appliedDiscounts: [],
    billLineItems: [],
    total: 0,
    totalBeforeDiscounts: 0,
  });

  filteredBillLineItems$ = computed<BillLineItemDtoWithPrice[]>(() => {
    const filter = this.filter$();
    return this.bills().billLineItems.filter(x => {
      if (filter === 'all') {
        return true;
      }
      return x.pricingType === filter;
    }) as BillLineItemDtoWithPrice[];
  });
  ngOnInit(): void {
    this.getbill();
  }

  async getbill() {
    let bill: BillDto = { ...this.data };
    if (this.data.id) {
      bill = await firstValueFrom(this.billService.getBill(this.data.id));
    }
    const r = {};
    let onceItems: BillLineItemDto[] = bill.billLineItems.filter(item => {
      return ['PricingItemKeys:DeviceInstallation', 'PricingItemKeys:Device'].includes(
        item.pricingItemKey
      );
    });
    let once = onceItems.reduce<BillLineItemDtoWithPrice>((acc, cu, index) => {
      console.log(acc);
      if (index == 0) {
        return { ...cu, price: cu.unitPrice * cu.quantity };
      } else {
        acc.pricingItemDisplayName = acc.pricingItemDisplayName + ' + ' + cu.pricingItemDisplayName;
        acc.price = (acc.price ? acc.price : 0) + cu.unitPrice * cu.quantity;
        acc.unitPrice = acc.unitPrice + cu.unitPrice;
        acc.quantity = acc.quantity + cu.quantity;
        return acc;
      }
    }, {} as BillLineItemDtoWithPrice);
    bill.billLineItems = bill.billLineItems
      .filter(item => {
        return !['PricingItemKeys:DeviceInstallation', 'PricingItemKeys:Device'].includes(
          item.pricingItemKey
        );
      })
      .map((x: BillLineItemDtoWithPrice) => {
        x.price = x.unitPrice * x.quantity;
        return x;
      });

    if (JSON.stringify(once) != '{}') {
      bill.billLineItems.push(once);
    }
    bill.billLineItems.map((x: BillLineItemDtoWithPrice) => {
      if (r[x.pricingType]) {
        r[x.pricingType]++;
      } else {
        r[x.pricingType] = 1;
      }
    });
    r['all'] = bill.billLineItems.length;
    this.pricingTypeOptionsCounts.set(r);
    this.bills.set(bill as BillDto & { billLineItems: BillLineItemDtoWithPrice[] });
  }

  closeDialog(data: boolean | undefined = undefined) {
    if (data == undefined) {
      this.dialogRef.close();
    } else {
      if (data) {
        this.dialogRef.close(this.data.id ?? this.data.requestId);
      } else {
        this.dialogRef.close(false);
      }
    }
  }
}

export const openPriceOfferDialog = (dialog: MatDialog, data: any) => {
  return dialog.open(PriceOfferDialogComponent, {
    data: data,
    height: '90%',
  });
};
